import { Box3, Group, Mesh, Object3D } from "three";
import { IEntityWindow } from "@layoutai/design_domain";
import { Object3DBase } from "./Object3DBase";
import { BoxMeshBuilder } from "./builder/BoxMeshBuilder";
import { MeshName } from "../const/MeshName";
import { UserDataKey } from "../const/UserDataKey";
import { ModelLoader } from "../utils/ModelLoader";
import { ResourceTracker } from "../utils/ResourceTracker";


/**
* @description 窗户 3D 对象
*/
export class Window3D extends Object3DBase {
    private _boxMesh: Mesh = BoxMeshBuilder.createBoxMesh();
    private _model: Object3D | undefined;

    constructor(uuid: string) {
        super(uuid);
        this._object3D = new Group();
        this._object3D.name = MeshName.WinDoor;
        this._object3D.add(this._boxMesh);
        this._object3D.userData[UserDataKey.EntityOfMesh] = true;
    }

    public get window(): IEntityWindow {
        return this.entity as IEntityWindow;
    }

    public async update(): Promise<Object3D | undefined> {
        this.updateBoxMesh();
        await this.updateModel();
        if (this._object3D) {
            this._object3D.userData[UserDataKey.EntityOfMesh] = this;
        }
        return this._object3D;
    }

    private updateBoxMesh() {
        this._boxMesh.visible = true;

        let pos = this.window.rect.rect_center_3d.clone();
        pos.z += this.window.height / 2;

        this._boxMesh.position.set(this.window.x, this.window.y, this.window.z+this.window.height / 2);
        this._boxMesh.rotation.set(0, 0, this.window.rect.rotation_z);
        this._boxMesh.scale.set(this.window.length, this.window.width, this.window.height);
    }

    private async updateModel() {
        if (this._model) {
            this._model.removeFromParent();
            ResourceTracker.disposeObject(this._model);
        }

        if (!this.window.materialId) {
            console.warn("Window3D.updateModel: 窗户没有材质ID", this.window);
            return;
        }
        let dvo = await this.window.materialInfo();
        if (!dvo) {
            console.warn("Window3D.updateModel: 窗户没有材质信息", this.window);
            return;
        }
        let groupNode = await ModelLoader.loadModel(dvo, false);
        if (!groupNode) {
            console.warn("Window3D.updateModel: 窗户加载失败", this.window);
            return;
        }

        if (this._object3D) {
            this._object3D.add(groupNode);
        }
        this.updateModelPose(groupNode);

        this._model = groupNode;
        // this._boxMesh.visible = false;
    }

    private updateModelPose(groupNode: Group) {
        let box = new Box3();
        box.setFromObject(groupNode);
        let boxLength = box.max.x - box.min.x;
        let boxWidth = box.max.y - box.min.y;
        let boxHeight = box.max.z - box.min.z;
        groupNode.position.set(this.window.x, this.window.y, this.window.z);
        groupNode.rotation.set(0, 0, this.window.rect.rotation_z);
        groupNode.scale.set(this.window.length / boxLength, this.window.width / boxWidth, this.window.height / boxHeight);
    }
}