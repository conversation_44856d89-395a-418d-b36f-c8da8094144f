import { Box3, <PERSON>, Mesh, Object3D } from "three";

import { IEntityFurniture } from "@layoutai/design_domain";

import { ModelLoader } from "../utils/ModelLoader";
import { ResourceTracker } from "../utils/ResourceTracker";
import { BoxMeshBuilder } from "./builder/BoxMeshBuilder";
import { Object3DBase } from "./Object3DBase";


/**
* @description 家具 3D 对象
* <AUTHOR>
* @date 2025-06-27 11:27:05
* @lastEditTime 2025-06-27 11:27:05
* @lastEditors xuld
*/
export class Furniture3D extends Object3DBase {
    private _boxMesh: Mesh = BoxMeshBuilder.createBoxMesh();
    private _model: Object3D | undefined;

    constructor(uuid: string) {
        super(uuid);
        this._object3D = new Group();
        this._object3D.add(this._boxMesh);
    }

    public get furniture(): IEntityFurniture {
        return this.entity as IEntityFurniture;
    }

    public async update(): Promise<Object3D | undefined> {
        this.updateBoxMesh();
        await this.updateModel();
        return this._object3D;
    }

    private updateBoxMesh() {
        this._boxMesh.visible = true;

        let pos = this.furniture.rect().rect_center_3d.clone();
        pos.z += this.furniture.height / 2;

        this._boxMesh.position.copy(pos as any);
        this._boxMesh.rotation.set(0, 0, this.furniture.rect().rotation_z);
        this._boxMesh.scale.set(this.furniture.length, this.furniture.width, this.furniture.height);
    }

    private async updateModel() {
        if (this._model) {
            this._model.removeFromParent();
            ResourceTracker.disposeObject(this._model);
        }

        if (!this.furniture.materialId) {
            console.warn("Furniture3D.updateModel: materialId is invalid", this.furniture.uuid, this.furniture.category);
            return;
        }
        let dvo = await this.furniture.materialInfo();
        if (!dvo) {
            console.warn("Furniture3D.updateModel: dvo is invalid", this.furniture.uuid, this.furniture.category);
            return;
        }
        let groupNode = await ModelLoader.loadModel(dvo, false);
        if (!groupNode) {
            console.warn("Furniture3D.updateModel: model is invalid", this.furniture.uuid, this.furniture.category);
            return;
        }

        if (this._object3D) {
            this._object3D.add(groupNode);
        }
        this.updateModelPose(groupNode);
        this._model = groupNode;
        // this._boxMesh.visible = false;
    }

    private updateModelPose(groupNode: Group) {
        let box = new Box3();
        box.setFromObject(groupNode);
        let boxLength = box.max.x - box.min.x;
        let boxWidth = box.max.y - box.min.y;
        let boxHeight = box.max.z - box.min.z;
        groupNode.position.set(this.furniture.x, this.furniture.y, this.furniture.z);
        groupNode.rotation.set(0, 0, this.furniture.rect().rotation_z);
        groupNode.scale.set(this.furniture.length / boxLength, this.furniture.width / boxWidth, this.furniture.height / boxHeight);
    }
}